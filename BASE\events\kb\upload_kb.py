import asyncio
import time
import uuid
from typing import Any, Callable, Coroutine, Optional
import socketio

from BASE.embeddings.embeddings import generate_embeddings_cloud
from BASE.chunkers.codebase import process_codebase_chunks
from BASE.chunkers.github import process_github_chunks
from BASE.chunkers.docs import process_docs_chunks
from BASE.chunkers.swagger import process_swagger_chunks
from BASE.chunkers.utils import make_chunks_from_file, make_chunks_from_content
from BASE.chunkers import create_chunk_metadata, create_chunk
from BASE.services.knowledge_bases import exists_knowledge_base_by_path, from_chunks
from logger.log import logger
import json


@logger.catch()
async def make_chunks_functional(
    upload_data: dict,
    progress_fn: Callable[[float], Coroutine[Any, Any, Any]],
    error_fn: Callable[[str], Coroutine[Any, Any, Any]],
) -> Optional[list[dict]]:
    """Create chunks using functional chunkers based on upload type."""
    await progress_fn(1)

    logger.info(f"upload_data: {upload_data}")

    kb_type = upload_data.get("type", "")
    metadata = upload_data.get("metadata", {})

    logger.info(f"Starting chunking process for knowledge base type: {kb_type}")

    if kb_type == "codebase":
        files = metadata.get("files", [])
        file_timestamps = metadata.get("file_timestamps", {})
        file_metadata = metadata.get("file", "")  # Extract file metadata
        logger.info(f"Processing {len(files)} codebase files")
        return await process_codebase_chunks(
            files, file_timestamps, progress_fn, file_metadata
        )
    elif kb_type == "Github":
        repo_url = metadata.get("repo_url", "")
        target_dir = metadata.get("target_dir", "/tmp/repos")
        logger.info(f"Processing Github repository: {repo_url}")
        return await process_github_chunks(repo_url, target_dir, progress_fn)
    elif kb_type == "Docs":
        urls = metadata.get("urls", [])
        logger.info(f"Processing {len(urls)} documentation URLs")
        return await process_docs_chunks(urls, progress_fn)
    elif kb_type == "Swagger":
        endpoints = metadata.get("endpoints", [])
        base_path = metadata.get("base_path", "/tmp/swagger")
        logger.info(f"Processing {len(endpoints)} Swagger endpoints")
        return await process_swagger_chunks(endpoints, base_path, progress_fn)
    elif kb_type == "DirectFiles":
        files = metadata.get("files", [])
        logger.info(f"Processing {len(files)} directly uploaded files")
        return await process_direct_files(files, progress_fn, error_fn)
    else:
        logger.error(f"Unknown knowledge base type: {kb_type}")
        await error_fn(f"Unknown knowledge base type: {kb_type}")
        return None


@logger.catch()
async def fill_embeddings_functional(
    chunks: list[dict],
    progress_fn: Callable[[float], Coroutine[Any, Any, Any]],
    error_fn: Callable[[str], Coroutine[Any, Any, Any]],
) -> Optional[list[dict]]:
    """Fill embeddings using cloud embeddings service."""
    if len(chunks) == 0:
        logger.warning("No chunks found for embedding generation")
        await error_fn("No chunks found")
        return None

    logger.info(
        f"Starting embedding generation phase using cloud embeddings for {len(chunks)} chunks"
    )

    await progress_fn(1)

    completed_chunks = 0

    async def process_batch(batch: list[dict]):
        nonlocal completed_chunks
        contents = [chunk["metadata"]["content"] for chunk in batch]
        logger.debug(f"Processing batch of {len(contents)} chunks for embeddings")
        embeddings = await generate_embeddings_cloud(batch=True, texts=contents)
        for chunk, embedding in zip(batch, embeddings):
            chunk["embeddings"] = embedding
            completed_chunks += 1
            await progress_fn(completed_chunks / len(chunks) * 100)

    embedding_phase_start = time.time()
    BATCH_SIZE = 10
    num_batches = (len(chunks) + BATCH_SIZE - 1) // BATCH_SIZE
    logger.info(f"Processing {num_batches} batches with batch size {BATCH_SIZE}")

    # use asyncio.gather to process batches in parallel
    await asyncio.gather(
        *[
            process_batch(chunks[i : i + BATCH_SIZE])
            for i in range(0, len(chunks), BATCH_SIZE)
        ]
    )
    embedding_phase_time = time.time() - embedding_phase_start
    avg_embedding_time = embedding_phase_time / len(chunks)

    logger.info(
        f"Embedding generation completed. Total chunks with embeddings: {len(chunks)} "
        f"in {embedding_phase_time:.2f}s (avg {avg_embedding_time:.3f}s per embedding)"
    )

    return chunks


@logger.catch()
async def process_direct_files(
    files: list[dict],
    progress_fn: Callable[[float], Coroutine[Any, Any, Any]],
    error_fn: Callable[[str], Coroutine[Any, Any, Any]],
) -> Optional[list[dict]]:
    """
    Process files directly uploaded by the user.

    Args:
        files: List of file dictionaries with structure:
            {
                "name": str,
                "content": str,
                "path": str (optional)
            }
        progress_fn: Progress callback function
        error_fn: Error callback function

    Returns:
        List of chunk dictionaries
    """
    if not files:
        logger.warning("No files provided for processing")
        await error_fn("No files provided")
        return None

    logger.info(f"Processing {len(files)} directly uploaded files")

    all_chunks = []
    processed_files = 0

    for file_data in files:
        try:
            file_name = file_data.get("name", "unknown_file")
            file_content = file_data.get("content", "")
            file_path = file_data.get("path", file_name)

            if not file_content:
                logger.warning(f"Skipping empty file: {file_name}")
                continue

            logger.debug(f"Processing file: {file_name}")

            # Determine file extension for language detection
            file_extension = ""
            if "." in file_name:
                file_extension = file_name.split(".")[-1]

            # Create chunks from content
            chunks = make_chunks_from_content(file_content, file_extension)

            # Update chunk metadata with file information
            for chunk in chunks:
                chunk["metadata"]["file"] = file_path
                chunk["metadata"][
                    "name"
                ] = f"{file_name}:{chunk['metadata'].get('additional_metadata', {}).get('line_start', 1)}-{chunk['metadata'].get('additional_metadata', {}).get('line_end', 1)}"

            all_chunks.extend(chunks)
            processed_files += 1

            # Report progress
            progress = (processed_files / len(files)) * 100
            await progress_fn(progress)

            logger.debug(f"Created {len(chunks)} chunks from file: {file_name}")

        except Exception as e:
            logger.error(
                f"Error processing file {file_data.get('name', 'unknown')}: {e}"
            )
            await error_fn(f"Error processing file: {str(e)}")
            continue

    logger.info(
        f"Successfully processed {processed_files} files, created {len(all_chunks)} total chunks"
    )
    return all_chunks


@logger.catch()
async def handle_upload_event(sio: socketio.AsyncServer, sid: str, data: dict = {}):
    """Handle upload events using functional programming approach."""

    # Extract upload data from the request
    upload_data = data
    request_id = upload_data.get("request_id", "")
    kb_name = upload_data.get("name", "")
    kb_type = upload_data.get("type", "")
    metadata = upload_data.get("metadata", {})

    logger.info(f"Starting knowledge base creation process for: {kb_name}")
    logger.info(f"Processing knowledge base type: {kb_type}")
    logger.debug(f"Request ID: {request_id}")

    # Path-based duplicate checking for Codebase type
    if kb_type == "codebase":
        kb_path = metadata.get("path", "")
        logger.info(f"kb_path: {kb_path}")
        exists = exists_knowledge_base_by_path(kb_path)
        if kb_path and exists:
            logger.error(f"Knowledge base with path '{kb_path}' already exists")
            await sio.emit(
                "upload:error",
                data={
                    "request_id": request_id,
                    "status": "error",
                    "message": f"A knowledge base with path '{kb_path}' already exists.",
                },
                to=sid,
            )
            return

    # Define processing steps
    async def step_1_chunking():
        return await make_chunks_functional(
            upload_data,
            progress_fn=lambda progress: sio.emit(
                to=sid,
                event="upload:progress",
                data={
                    "request_id": request_id,
                    "status": "PROGRESS",
                    "progress": progress,
                    "message": "(1/3) Chunking files",
                },
            ),
            error_fn=lambda message: sio.emit(
                to=sid,
                event="upload:error",
                data={
                    "request_id": request_id,
                    "status": "error",
                    "message": message,
                },
            ),
        )

    async def step_2_embeddings(chunks):
        return await fill_embeddings_functional(
            chunks,
            progress_fn=lambda progress: sio.emit(
                to=sid,
                event="upload:progress",
                data={
                    "request_id": request_id,
                    "status": "PROGRESS",
                    "progress": progress,
                    "message": "(2/3) Generating embeddings",
                },
            ),
            error_fn=lambda message: sio.emit(
                to=sid,
                event="upload:error",
                data={
                    "request_id": request_id,
                    "status": "error",
                    "message": message,
                },
            ),
        )

    # Execute processing steps
    logger.info("Starting step 1: Chunking files")
    chunks = await step_1_chunking()
    if chunks is None:
        logger.error("No chunks returned from chunking step")
        return

    logger.info(f"Step 1 completed. Generated {len(chunks)} chunks")
    logger.info("Starting step 2: Generating embeddings")
    chunks = await step_2_embeddings(chunks)
    if chunks is None:
        logger.error("No chunks returned from embedding step")
        return

    logger.info(f"Step 2 completed. {len(chunks)} chunks with embeddings")

    # -----------------------------------------------------------------------------------------
    # 3. Create Knowledge Base ----------------------------------------------------------------
    # -----------------------------------------------------------------------------------------

    metadata_phase_start = time.time()
    logger.info(
        "Starting step 3: Creating knowledge base and storing in vector database"
    )
    await sio.emit(
        to=sid,
        event="upload:progress",
        data={
            "request_id": request_id,
            "status": "PROGRESS",
            "progress": 0,
            "message": "(3/3) Creating knowledge base",
        },
    )

    db_creation_start = time.time()
    logger.info("Creating knowledge base entry in local database")

    # Generate unique KB ID for security (never accept external IDs)
    kb_id = str(uuid.uuid4())
    logger.info(f"Generated knowledge base ID: {kb_id}")

    # Create complete metadata structure inline following your Pydantic model structure
    complete_metadata = {
        # Base QdrantKnowledgeBaseMetadata fields
        "id": kb_id,
        "cloud_id": None,  # Always None for new knowledge bases
        "name": kb_name,
        "description": upload_data.get(
            "description", ""
        ),  # Default to empty string if not provided
        "type": kb_type,
        "source": upload_data.get("source", "Local"),
        "scope": upload_data.get("scope", "personal"),
        "syncConfig": {
            "enabled": upload_data.get("syncConfig", {}).get("enabled", False),
            "lastSynced": upload_data.get("syncConfig", {}).get("lastSynced", 0),
        },
        "isAutoIndexed": upload_data.get("isAutoIndexed", False),
        "metadata": None,  # Will be populated with type-specific metadata below
        "status": "ready",
    }

    # Create type-specific metadata based on KB type (nested in metadata field)
    if kb_type == "codebase":
        files = metadata.get("files", [])
        file_timestamps = metadata.get("file_timestamps", {})

        # Generate current timestamp for files if not provided
        if not file_timestamps and files:
            current_timestamp = int(
                time.time() * 1000
            )  # Unix timestamp in milliseconds
            file_timestamps = {file_path: current_timestamp for file_path in files}
            logger.info(
                f"Generated timestamps for {len(files)} files using current time: {current_timestamp}"
            )

        # QdrantCodebaseMetadata structure
        complete_metadata["metadata"] = {
            "path": metadata.get("path", ""),
            "files": files,
            "file_timestamps": file_timestamps,
        }
    elif kb_type == "git":
        # QdrantGithubMetadata structure
        complete_metadata["metadata"] = {
            "repo_url": metadata.get("repo_url", ""),
            "branch": metadata.get("branch", ""),
            "accessToken": metadata.get("accessToken"),
        }
    elif kb_type == "docs":
        # QdrantDocsMetadata structure
        complete_metadata["metadata"] = {"urls": metadata.get("urls", [])}
    elif kb_type == "swagger":
        # QdrantSwaggerMetadata structure
        complete_metadata["metadata"] = {
            "endpoints": metadata.get("endpoints", []),
            "source_type": metadata.get("source_type", ""),
            "source_value": metadata.get("source_value", ""),
        }

        complete_metadata["metadata"] = {
            "files": [
                file_data.get("name", f"file_{i}") for i, file_data in enumerate(files)
            ],
            "file_timestamps": file_timestamps,
        }

    logger.debug(
        f"Complete knowledge base metadata prepared with fields: {list(complete_metadata.keys())}"
    )

    # Store chunks and metadata using the functional approach
    logger.info("Creating knowledge base using functional from_chunks approach")
    db_creation_start = time.time()

    logger.info(f"complte_metadata: {json.dumps(complete_metadata, indent=2)}")

    try:
        kb_result = await from_chunks(metadata=complete_metadata, chunks=chunks)
        logger.info(f"Knowledge base created successfully: {kb_result['name']}")
    except Exception as e:
        logger.error(f"Failed to create knowledge base: {e}")
        await sio.emit(
            to=sid,
            event="upload:error",
            data={
                "request_id": request_id,
                "status": "error",
                "message": f"Failed to create knowledge base: {str(e)}",
            },
        )
        return

    db_creation_time = time.time() - db_creation_start
    metadata_phase_time = time.time() - metadata_phase_start

    logger.info(
        f"Knowledge base created successfully with {len(chunks)} chunks "
        f"(DB creation: {db_creation_time:.2f}s, total metadata phase: {metadata_phase_time:.2f}s)"
    )

    # -----------------------------------------------------------------------------------------
    # 6. Report success to the client -------------------------------------------------------
    # -----------------------------------------------------------------------------------------

    logger.info(f"Upload process completed successfully for knowledge base: {kb_name}")
    logger.info(f"Processed {len(chunks)} chunks")

    # Report success to the client
    await sio.emit(
        event="upload:success",
        to=sid,
        data={
            "request_id": request_id,
            "status": "success",
            "message": "Knowledgebase prepared",
            "data": kb_result,
        },
    )
    logger.info(f"Success event emitted to client for request: {request_id}")
    logger.info(
        f"Knowledge base upload workflow completed for: {kb_name} (ID: {kb_id})"
    )
