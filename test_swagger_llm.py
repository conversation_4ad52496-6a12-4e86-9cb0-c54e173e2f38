#!/usr/bin/env python3
"""
Test script for the enhanced Swagger streaming functionality with LLM integration.
"""

import asyncio
import json
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from BASE.services.swagger.llm_service import generate_queries, generate_calls, structure_output


async def test_generate_queries():
    """Test the generate_queries function."""
    print("Testing generate_queries...")
    
    llm_config = {
        "base_url": "https://backend.v3.codemateai.dev/v1",
        "api_key": "sk-DS1Y-HT1DnXEhLyKckXQ6A",
        "model": "gpt-4.1-mini"
    }
    
    query = "How do I create a new user account and then update their profile information?"
    
    try:
        queries = await generate_queries(query, llm_config, num_prompts=3)
        print(f"Generated queries: {queries}")
        return True
    except Exception as e:
        print(f"Error testing generate_queries: {e}")
        return False


async def test_generate_calls():
    """Test the generate_calls function."""
    print("Testing generate_calls...")
    
    llm_config = {
        "base_url": "https://backend.v3.codemateai.dev/v1",
        "api_key": "sk-DS1Y-HT1DnXEhLyKckXQ6A",
        "model": "gpt-4.1-mini"
    }
    
    query = "Create a user and update their profile"
    search_results = []
    spec_endpoints = [
        {"path": "/users", "method": "POST", "summary": "Create a new user"},
        {"path": "/users/{id}", "method": "PUT", "summary": "Update user profile"}
    ]
    
    try:
        calls = await generate_calls(query, search_results, spec_endpoints, llm_config)
        print(f"Generated calls: {json.dumps(calls, indent=2)}")
        return True
    except Exception as e:
        print(f"Error testing generate_calls: {e}")
        return False


async def test_structure_output():
    """Test the structure_output function."""
    print("Testing structure_output...")
    
    llm_config = {
        "base_url": "https://backend.v3.codemateai.dev/v1",
        "api_key": "sk-DS1Y-HT1DnXEhLyKckXQ6A",
        "model": "gpt-4.1-mini"
    }
    
    structure_prompt = "Format the output as a step-by-step guide"
    swagger_output = {
        "name": "User API",
        "calls": [
            {"name": "create_user", "sequence": 1, "endpoint": "/users", "method": "POST"},
            {"name": "update_user", "sequence": 2, "endpoint": "/users/{id}", "method": "PUT"}
        ]
    }
    
    try:
        structured = await structure_output(structure_prompt, swagger_output, llm_config)
        print(f"Structured output: {json.dumps(structured, indent=2)}")
        return True
    except Exception as e:
        print(f"Error testing structure_output: {e}")
        return False


async def main():
    """Run all tests."""
    print("Starting Swagger LLM service tests...\n")
    
    tests = [
        test_generate_queries,
        test_generate_calls,
        test_structure_output
    ]
    
    results = []
    for test in tests:
        try:
            result = await test()
            results.append(result)
            print(f"✓ {test.__name__}: {'PASSED' if result else 'FAILED'}\n")
        except Exception as e:
            print(f"✗ {test.__name__}: FAILED with exception: {e}\n")
            results.append(False)
    
    passed = sum(results)
    total = len(results)
    
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
        return 0
    else:
        print("❌ Some tests failed")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
