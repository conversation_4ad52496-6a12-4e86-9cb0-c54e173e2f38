import json
from BASE.embeddings.embeddings import generate_embeddings_cloud
import requests
from constants import general
from ipc import IPC

ipc_ = IPC.connect()

# -----------------------------------------------------------------------------
# Perform web search
# -----------------------------------------------------------------------------


async def process_web_search(
    *, query: str, tool_id: str, search_references: any, 
):
    """
    Process a web search request.

    Args:
        query: Search query string
        tool_id: ID of the tool making the request
        session: Session ID for authentication
        search_references: SearchReferences object to track search results

    Returns:
        Tuple of (message, search_references)
    """
    try:
        response = requests.post(
            f"{general}/web_search",
            json={"query": query},
            headers={
                "Content-Type": "application/json",
                "x-session": ipc_.get("current_session"),
            },
        )
        data = response.json()
        sources = data["data"]["sources"]

        for source in sources:
            search_references.add_search_result(
                path=source["url"], name=source["title"], content="", type="web"
            )

        message = [
            {
                "role": "assistant",
                "action": {"id": tool_id, "name": "web_search", "arguments": ""},
            },
            {
                "role": "action",
                "content": data["data"]["content"],
                "action_id": tool_id,
            },
        ]

        return message, search_references
    except Exception as e:
        raise
