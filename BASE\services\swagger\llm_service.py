from litellm import acompletion
import json
import re
from typing import List, Dict, Any
from logger.log import logger


async def generate_queries(query: str, llm_config: dict, num_prompts: int = 5) -> List[str]:
    """
    Generate search queries for Swagger API documentation using LLM.
    
    Args:
        query: User's original query
        llm_config: LLM configuration dict with base_url, api_key, model
        num_prompts: Number of search prompts to generate
        
    Returns:
        List of generated search prompts
    """
    try:
        logger.info(f"Generating {num_prompts} search queries for: {query[:100]}...")
        
        system_prompt = (
            "You are an AI that helps in searching Swagger API documentation. "
            "Given a user query, break it down into multiple precise search prompts "
            "that can be used to find relevant API endpoints. "
            "Each prompt should focus on a specific aspect of the user's request. "
            "Return the prompts as a numbered list, one per line."
        )
        
        user_prompt = f"""Break down the following query into {num_prompts} concise search prompts:

{query}

Generate specific search terms that would help find relevant API endpoints, parameters, and responses."""

        # Configure model name
        model_name = llm_config.get("model")
        if not model_name.startswith(("openai/", "anthropic/", "cohere/", "huggingface/")):
            model_name = f"openai/{model_name}"

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

        response = await acompletion(
            base_url=llm_config.get("base_url", "https://llm.codemate.ai/v1"),
            api_key=llm_config.get("api_key", ""),
            model=model_name,
            messages=messages,
            temperature=0.3,
            stream=False
        )

        if hasattr(response, 'choices') and len(response.choices) > 0:
            content = response.choices[0].message.content
            
            # Parse the numbered list response
            sub_prompts = []
            lines = content.strip().split('\n')
            
            for line in lines:
                line = line.strip()
                if line:
                    # Remove numbering (1., 2., etc.) and clean up
                    cleaned_line = re.sub(r'^\d+\.\s*', '', line)
                    cleaned_line = cleaned_line.strip('- ')
                    if cleaned_line:
                        sub_prompts.append(cleaned_line)
            
            logger.info(f"Generated {len(sub_prompts)} search queries")
            return sub_prompts[:num_prompts]  # Ensure we don't exceed requested number
        
        logger.warning("No valid response from LLM for query generation")
        return [query]  # Fallback to original query
        
    except Exception as e:
        logger.error(f"Error generating search queries: {e}")
        return [query]  # Fallback to original query


async def generate_calls(query: str, search_results: List[Dict], spec_endpoints: List[Dict], llm_config: dict) -> List[Dict]:
    """
    Generate API calls based on user query and search results using LLM.
    
    Args:
        query: User's original query
        search_results: Results from swagger search
        spec_endpoints: Available endpoints from swagger spec
        llm_config: LLM configuration dict
        
    Returns:
        List of generated API call sequences
    """
    try:
        logger.info(f"Generating API calls for query: {query[:100]}...")
        
        system_prompt = """You are an API integration expert. Given a user query and available API endpoints, 
generate a sequence of API calls that would fulfill the user's request.

For each API call, provide:
1. The endpoint path
2. The HTTP method
3. Required parameters
4. The sequence order
5. Brief description of what this call does

Return your response as a JSON array of objects with this structure:
{
  "path": "/api/endpoint",
  "method": "GET",
  "sequence": 1,
  "description": "What this call does",
  "parameters": {...}
}"""

        # Prepare context about available endpoints
        endpoints_context = ""
        if spec_endpoints:
            endpoints_context = "Available API endpoints:\n"
            for i, endpoint in enumerate(spec_endpoints[:10]):  # Limit to first 10 to avoid token limits
                endpoints_context += f"- {endpoint.get('method', 'GET')} {endpoint.get('path', 'unknown')}\n"
        
        # Prepare search results context
        search_context = ""
        if search_results:
            search_context = "Relevant search results:\n"
            for i, result in enumerate(search_results[:5]):  # Limit to first 5
                if 'metadata' in result and 'endpoint' in result['metadata']:
                    endpoint = result['metadata']['endpoint']
                    search_context += f"- {endpoint.get('method', 'GET')} {endpoint.get('path', 'unknown')}: {endpoint.get('summary', 'No description')}\n"

        user_prompt = f"""User Query: {query}

{endpoints_context}

{search_context}

Based on the user query and available endpoints, generate a sequence of API calls that would fulfill the request. 
Return only the JSON array, no additional text."""

        # Configure model name
        model_name = llm_config.get("model")
        if not model_name.startswith(("openai/", "anthropic/", "cohere/", "huggingface/")):
            model_name = f"openai/{model_name}"

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

        response = await acompletion(
            base_url=llm_config.get("base_url", "https://llm.codemate.ai/v1"),
            api_key=llm_config.get("api_key", ""),
            model=model_name,
            messages=messages,
            temperature=0.4,
            stream=False
        )

        if hasattr(response, 'choices') and len(response.choices) > 0:
            content = response.choices[0].message.content
            
            try:
                # Try to parse JSON response
                if content.strip().startswith('['):
                    calls = json.loads(content)
                    logger.info(f"Generated {len(calls)} API calls")
                    return calls
                else:
                    # Try to extract JSON from response
                    json_match = re.search(r'\[.*\]', content, re.DOTALL)
                    if json_match:
                        calls = json.loads(json_match.group())
                        logger.info(f"Generated {len(calls)} API calls")
                        return calls
                    
            except json.JSONDecodeError as e:
                logger.warning(f"Failed to parse JSON response: {e}")
        
        logger.warning("No valid API calls generated")
        return []
        
    except Exception as e:
        logger.error(f"Error generating API calls: {e}")
        return []


async def structure_output(structure_prompt: str, swagger_output: Dict, llm_config: dict) -> Dict:
    """
    Apply structure formatting to swagger output using LLM.
    
    Args:
        structure_prompt: Instructions for how to structure the output
        swagger_output: The swagger response to be structured
        llm_config: LLM configuration dict
        
    Returns:
        Structured output dictionary
    """
    try:
        logger.info("Applying structure to swagger output")
        
        system_prompt = """You are a data formatting expert. Given a structure prompt and swagger API output, 
reformat the output according to the specified structure while preserving all important information.

Return the result as valid JSON that follows the requested structure."""

        user_prompt = f"""Structure Instructions: {structure_prompt}

Original Swagger Output:
{json.dumps(swagger_output, indent=2)}

Please reformat this output according to the structure instructions. Return only the JSON result."""

        # Configure model name
        model_name = llm_config.get("model")
        if not model_name.startswith(("openai/", "anthropic/", "cohere/", "huggingface/")):
            model_name = f"openai/{model_name}"

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

        response = await acompletion(
            base_url=llm_config.get("base_url", "https://llm.codemate.ai/v1"),
            api_key=llm_config.get("api_key", ""),
            model=model_name,
            messages=messages,
            temperature=0.2,
            stream=False
        )

        if hasattr(response, 'choices') and len(response.choices) > 0:
            content = response.choices[0].message.content
            
            try:
                # Try to parse JSON response
                if content.strip().startswith('{'):
                    structured_output = json.loads(content)
                    logger.info("Successfully applied structure to output")
                    return structured_output
                else:
                    # Try to extract JSON from response
                    json_match = re.search(r'\{.*\}', content, re.DOTALL)
                    if json_match:
                        structured_output = json.loads(json_match.group())
                        logger.info("Successfully applied structure to output")
                        return structured_output
                    
            except json.JSONDecodeError as e:
                logger.warning(f"Failed to parse structured JSON response: {e}")
        
        logger.warning("Failed to apply structure, returning original output")
        return swagger_output
        
    except Exception as e:
        logger.error(f"Error applying structure to output: {e}")
        return swagger_output
